package main

import (
	"fmt"
	"os"
)

func main() {
	if len(os.Args) < 2 {
		fmt.Println("Usage: go run debug_header_size.go <file>")
		os.Exit(1)
	}
	
	filename := os.Args[1]
	
	file, err := os.Open(filename)
	if err != nil {
		fmt.Printf("Error opening file: %v\n", err)
		os.Exit(1)
	}
	defer file.Close()
	
	// Read first 64 bytes
	buffer := make([]byte, 64)
	n, err := file.Read(buffer)
	if err != nil {
		fmt.Printf("Error reading: %v\n", err)
		os.Exit(1)
	}
	
	fmt.Printf("Analyzing header structure:\n")
	
	// Parse EBML header
	pos := 0
	fmt.Printf("EBML Header at position %d:\n", pos)
	
	// EBML ID: 1a 45 df a3 (4 bytes)
	ebmlID := (uint32(buffer[0]) << 24) | (uint32(buffer[1]) << 16) | (uint32(buffer[2]) << 8) | uint32(buffer[3])
	fmt.Printf("  EBML ID: 0x%08X\n", ebmlID)
	pos += 4
	
	// EBML Size: a3 (1 byte, value = 35)
	ebmlSize := uint64(buffer[4] & 0x7F) // Remove marker bit
	fmt.Printf("  EBML Size: %d (from byte 0x%02X)\n", ebmlSize, buffer[4])
	pos += 1
	
	// Skip EBML content
	pos += int(ebmlSize)
	fmt.Printf("  EBML content ends at position %d\n", pos)
	
	// Parse Segment header
	fmt.Printf("\nSegment Header at position %d:\n", pos)
	
	// Segment ID: 18 53 80 67 (4 bytes)
	segmentID := (uint32(buffer[pos]) << 24) | (uint32(buffer[pos+1]) << 16) | (uint32(buffer[pos+2]) << 8) | uint32(buffer[pos+3])
	fmt.Printf("  Segment ID: 0x%08X\n", segmentID)
	pos += 4
	
	// Segment Size: 01 00 00 00 a5 a8 bc 6f (8 bytes)
	// First byte is 01, which means 8-byte VINT
	segmentSizeBytes := buffer[pos:pos+8]
	fmt.Printf("  Segment Size bytes: %02x %02x %02x %02x %02x %02x %02x %02x\n",
		segmentSizeBytes[0], segmentSizeBytes[1], segmentSizeBytes[2], segmentSizeBytes[3],
		segmentSizeBytes[4], segmentSizeBytes[5], segmentSizeBytes[6], segmentSizeBytes[7])
	
	// Parse 8-byte VINT (remove marker bit from first byte)
	segmentSize := uint64(segmentSizeBytes[0] & 0x7F)
	for i := 1; i < 8; i++ {
		segmentSize = (segmentSize << 8) | uint64(segmentSizeBytes[i])
	}
	fmt.Printf("  Segment Size: %d\n", segmentSize)
	pos += 8
	
	fmt.Printf("  Segment data starts at position %d\n", pos)
	
	// Show first few bytes of segment data
	fmt.Printf("\nFirst segment data bytes:\n")
	for i := 0; i < 16 && pos+i < n; i++ {
		fmt.Printf("  [%d] 0x%02X\n", i, buffer[pos+i])
	}
	
	// Try to parse first element in segment
	fmt.Printf("\nParsing first element in segment:\n")
	if pos < n {
		firstByte := buffer[pos]
		fmt.Printf("  First byte: 0x%02X (binary: %08b)\n", firstByte, firstByte)
		
		// Find marker bit
		var width int
		for i := 7; i >= 0; i-- {
			if (firstByte & (1 << i)) != 0 {
				width = 8 - i
				break
			}
		}
		fmt.Printf("  VINT width: %d bytes\n", width)
		
		if width > 0 && pos+width <= n {
			// Parse as Element ID (keep marker bit)
			elementID := uint64(firstByte)
			for i := 1; i < width; i++ {
				elementID = (elementID << 8) | uint64(buffer[pos+i])
			}
			fmt.Printf("  Element ID: 0x%08X\n", uint32(elementID))
			
			// Check if this is a known element
			elementName := getElementName(uint32(elementID))
			fmt.Printf("  Element Name: %s\n", elementName)
		}
	}
}

func getElementName(id uint32) string {
	switch id {
	case 0x114D9B74:
		return "SeekHead"
	case 0x1549A966:
		return "SegmentInfo"
	case 0x1654AE6B:
		return "Tracks"
	case 0x1C53BB6B:
		return "Cues"
	case 0x1941A469:
		return "Attachments"
	case 0x1043A770:
		return "Chapters"
	case 0x1254C367:
		return "Tags"
	case 0x1F43B675:
		return "Cluster"
	default:
		return "Unknown"
	}
}
