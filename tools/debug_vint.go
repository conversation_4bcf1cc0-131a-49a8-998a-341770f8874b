package main

import (
	"fmt"
	"os"
)

func main() {
	if len(os.Args) < 2 {
		fmt.Println("Usage: go run debug_vint.go <file>")
		os.Exit(1)
	}
	
	filename := os.Args[1]
	
	file, err := os.Open(filename)
	if err != nil {
		fmt.Printf("Error opening file: %v\n", err)
		os.Exit(1)
	}
	defer file.Close()
	
	fmt.Printf("Debugging VINT parsing for: %s\n", filename)
	
	// Skip to Segment data (EBML header + Segment header = 50 bytes)
	file.Seek(50, 0)
	
	// Read next 32 bytes to analyze
	buffer := make([]byte, 32)
	n, err := file.Read(buffer)
	if err != nil {
		fmt.Printf("Error reading: %v\n", err)
		os.Exit(1)
	}
	
	fmt.Printf("Read %d bytes from Segment data:\n", n)
	
	// Print hex dump
	for i := 0; i < n; i += 16 {
		fmt.Printf("%08x  ", 50+i)
		
		for j := 0; j < 16 && i+j < n; j++ {
			if j == 8 {
				fmt.Print(" ")
			}
			fmt.Printf("%02x ", buffer[i+j])
		}
		
		for j := n - i; j < 16; j++ {
			if j == 8 {
				fmt.Print(" ")
			}
			fmt.Print("   ")
		}
		
		fmt.Print(" |")
		for j := 0; j < 16 && i+j < n; j++ {
			b := buffer[i+j]
			if b >= 32 && b <= 126 {
				fmt.Printf("%c", b)
			} else {
				fmt.Print(".")
			}
		}
		fmt.Print("|\n")
	}
	
	// Now let's manually parse the first few VINTs
	fmt.Println("\nManual VINT parsing:")
	
	pos := 0
	for elementNum := 0; elementNum < 5 && pos < len(buffer); elementNum++ {
		fmt.Printf("\nElement %d at position %d:\n", elementNum, pos)
		
		if pos >= len(buffer) {
			break
		}
		
		// Parse Element ID
		fmt.Printf("  Parsing Element ID from byte 0x%02x\n", buffer[pos])
		id, idSize := parseElementID(buffer[pos:])
		fmt.Printf("  Element ID: 0x%08X (size: %d bytes)\n", id, idSize)
		pos += idSize
		
		if pos >= len(buffer) {
			break
		}
		
		// Parse Element Size
		fmt.Printf("  Parsing Element Size from byte 0x%02x\n", buffer[pos])
		size, sizeSize := parseVINT(buffer[pos:])
		fmt.Printf("  Element Size: %d (size: %d bytes)\n", size, sizeSize)
		pos += sizeSize
		
		// Skip element data
		if size > 0 && size < 1000000 { // reasonable size
			pos += int(size)
			fmt.Printf("  Skipping %d bytes of element data\n", size)
		} else {
			fmt.Printf("  Element size too large or invalid, stopping\n")
			break
		}
	}
}

func parseElementID(data []byte) (uint32, int) {
	if len(data) == 0 {
		return 0, 0
	}
	
	first := data[0]
	if first == 0 {
		return 0, 0
	}
	
	var width int
	for i := 7; i >= 0; i-- {
		if (first & (1 << i)) != 0 {
			width = 8 - i
			break
		}
	}
	
	if width == 0 || width > len(data) {
		return 0, 0
	}
	
	// For Element IDs, we keep the marker bit
	value := uint64(first)
	for i := 1; i < width; i++ {
		value = (value << 8) | uint64(data[i])
	}
	
	return uint32(value), width
}

func parseVINT(data []byte) (uint64, int) {
	if len(data) == 0 {
		return 0, 0
	}
	
	first := data[0]
	if first == 0 {
		return 0, 0
	}
	
	var width int
	var mask byte
	for i := 7; i >= 0; i-- {
		if (first & (1 << i)) != 0 {
			width = 8 - i
			mask = byte((1 << i) - 1)
			break
		}
	}
	
	if width == 0 || width > len(data) {
		return 0, 0
	}
	
	// For sizes, we remove the marker bit
	value := uint64(first & mask)
	for i := 1; i < width; i++ {
		value = (value << 8) | uint64(data[i])
	}
	
	return value, width
}
