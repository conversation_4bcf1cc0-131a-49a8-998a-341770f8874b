package main

import (
	"fmt"
	"os"
)

func main() {
	if len(os.Args) < 2 {
		fmt.Println("Usage: go run debug_file.go <file>")
		os.Exit(1)
	}
	
	filename := os.Args[1]
	
	file, err := os.Open(filename)
	if err != nil {
		fmt.Printf("Error opening file: %v\n", err)
		os.Exit(1)
	}
	defer file.Close()
	
	// Read first 32 bytes
	buffer := make([]byte, 32)
	n, err := file.Read(buffer)
	if err != nil {
		fmt.Printf("Error reading file: %v\n", err)
		os.Exit(1)
	}
	
	fmt.Printf("File: %s\n", filename)
	fmt.Printf("Read %d bytes\n", n)
	fmt.Printf("Hex dump of first 32 bytes:\n")
	
	for i := 0; i < n; i += 16 {
		// Print offset
		fmt.Printf("%08x  ", i)
		
		// Print hex bytes
		for j := 0; j < 16 && i+j < n; j++ {
			if j == 8 {
				fmt.Print(" ")
			}
			fmt.Printf("%02x ", buffer[i+j])
		}
		
		// Pad if needed
		for j := n - i; j < 16; j++ {
			if j == 8 {
				fmt.Print(" ")
			}
			fmt.Print("   ")
		}
		
		// Print ASCII representation
		fmt.Print(" |")
		for j := 0; j < 16 && i+j < n; j++ {
			b := buffer[i+j]
			if b >= 32 && b <= 126 {
				fmt.Printf("%c", b)
			} else {
				fmt.Print(".")
			}
		}
		fmt.Print("|\n")
	}
	
	// Check for EBML header
	fmt.Printf("\nEBML Header Analysis:\n")
	if n >= 4 {
		// EBML header should start with 0x1A45DFA3
		expectedHeader := []byte{0x1A, 0x45, 0xDF, 0xA3}
		if n >= 4 && 
		   buffer[0] == expectedHeader[0] && 
		   buffer[1] == expectedHeader[1] && 
		   buffer[2] == expectedHeader[2] && 
		   buffer[3] == expectedHeader[3] {
			fmt.Println("✓ Valid EBML header found")
		} else {
			fmt.Printf("✗ Invalid EBML header. Expected: %02x %02x %02x %02x, Got: %02x %02x %02x %02x\n",
				expectedHeader[0], expectedHeader[1], expectedHeader[2], expectedHeader[3],
				buffer[0], buffer[1], buffer[2], buffer[3])
		}
	} else {
		fmt.Println("✗ File too short to contain EBML header")
	}
	
	// Get file size
	stat, err := file.Stat()
	if err == nil {
		fmt.Printf("\nFile size: %d bytes (%.2f MB)\n", stat.Size(), float64(stat.Size())/(1024*1024))
	}
}
