#!/bin/bash

# Test script for MKV track splitter
# This script tests the track splitter with the provided MKV file

set -e  # Exit on any error

# Configuration
INPUT_FILE="/Volumes/storage/Downloads/upload/Dexter.Resurrection.S01E05.Murder.Horny.2160p.PMTP.WEB-DL.DDP5.1.DV.HDR.H.265-NTb.mkv"
OUTPUT_DIR="./test_output"
LOG_FILE="./test_log.txt"

echo "=== MKV Track Splitter Test ==="
echo "Input file: $INPUT_FILE"
echo "Output directory: $OUTPUT_DIR"
echo "Log file: $LOG_FILE"
echo

# Check if input file exists
if [ ! -f "$INPUT_FILE" ]; then
    echo "Error: Input file does not exist: $INPUT_FILE"
    echo "Please check the file path and try again."
    exit 1
fi

# Clean up previous test results
if [ -d "$OUTPUT_DIR" ]; then
    echo "Cleaning up previous test results..."
    rm -rf "$OUTPUT_DIR"
fi

# Create output directory
mkdir -p "$OUTPUT_DIR"

# Build the Go program
echo "Building track splitter..."
go mod tidy
go build -o track_splitter track_splitter.go

if [ $? -ne 0 ]; then
    echo "Error: Failed to build track splitter"
    exit 1
fi

echo "Build successful!"
echo

# Run the track splitter and capture output
echo "Running track splitter..."
echo "This may take a while depending on the file size..."
echo

# Run with both stdout and stderr captured
./track_splitter "$INPUT_FILE" "$OUTPUT_DIR" 2>&1 | tee "$LOG_FILE"

# Check if the command was successful
if [ ${PIPESTATUS[0]} -eq 0 ]; then
    echo
    echo "=== Test Results ==="
    echo "Track splitting completed successfully!"
    echo
    
    # List output files
    echo "Generated files:"
    ls -la "$OUTPUT_DIR"
    echo
    
    # Show file sizes
    echo "File sizes:"
    du -h "$OUTPUT_DIR"/*
    echo
    
    # Count total files
    file_count=$(ls -1 "$OUTPUT_DIR" | wc -l)
    echo "Total files created: $file_count"
    
    # Show summary from log
    echo
    echo "=== Summary from log ==="
    grep -E "(Track [0-9]+:|Total packets|Packets per track)" "$LOG_FILE" || true
    
else
    echo
    echo "=== Test Failed ==="
    echo "Track splitter failed with exit code: ${PIPESTATUS[0]}"
    echo "Check the log file for details: $LOG_FILE"
    echo
    echo "Last few lines of log:"
    tail -20 "$LOG_FILE"
    exit 1
fi

echo
echo "=== Test completed successfully! ==="
echo "Check the output directory for extracted tracks: $OUTPUT_DIR"
echo "Full log available at: $LOG_FILE"
