package main

import (
	"fmt"
	"os"

	matroska "github.com/dwbuiten/matroska"
)

func main() {
	if len(os.Args) < 2 {
		fmt.Println("Usage: go run debug_segment.go <file>")
		os.Exit(1)
	}
	
	filename := os.Args[1]
	
	file, err := os.Open(filename)
	if err != nil {
		fmt.Printf("Error opening file: %v\n", err)
		os.Exit(1)
	}
	defer file.Close()
	
	fmt.Printf("Debugging segment parsing for: %s\n", filename)
	
	// Create demuxer
	demuxer, err := matroska.NewDemuxer(file)
	if err != nil {
		fmt.Printf("Error creating demuxer: %v\n", err)
		os.Exit(1)
	}
	defer demuxer.Close()
	
	fmt.Println("Demuxer created successfully!")
	
	// Try to get file info
	fileInfo, err := demuxer.GetFileInfo()
	if err != nil {
		fmt.Printf("Error getting file info: %v\n", err)
	} else {
		fmt.Printf("File info found!\n")
		fmt.Printf("  Title: %s\n", fileInfo.Title)
		fmt.Printf("  Duration: %d\n", fileInfo.Duration)
		fmt.Printf("  Timecode Scale: %d\n", fileInfo.TimecodeScale)
	}
	
	// Try to get track count
	numTracks, err := demuxer.GetNumTracks()
	if err != nil {
		fmt.Printf("Error getting track count: %v\n", err)
	} else {
		fmt.Printf("Number of tracks: %d\n", numTracks)
		
		// Get track info for each track
		for i := uint(0); i < numTracks; i++ {
			trackInfo, err := demuxer.GetTrackInfo(i)
			if err != nil {
				fmt.Printf("Error getting track %d info: %v\n", i, err)
				continue
			}
			
			fmt.Printf("Track %d:\n", i)
			fmt.Printf("  Number: %d\n", trackInfo.Number)
			fmt.Printf("  Type: %d\n", trackInfo.Type)
			fmt.Printf("  Codec: %s\n", trackInfo.CodecID)
			fmt.Printf("  Name: %s\n", trackInfo.Name)
			fmt.Printf("  Language: %s\n", trackInfo.Language)
		}
	}
	
	// Try to read a few packets
	fmt.Println("\nTrying to read packets...")
	for i := 0; i < 5; i++ {
		packet, err := demuxer.ReadPacket()
		if err != nil {
			fmt.Printf("Error reading packet %d: %v\n", i, err)
			break
		}
		
		if packet != nil {
			fmt.Printf("Packet %d: Track=%d, StartTime=%d, Size=%d\n", 
				i, packet.Track, packet.StartTime, len(packet.Data))
		}
	}
}
