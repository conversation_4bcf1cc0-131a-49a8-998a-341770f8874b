package main

import (
	"fmt"
	"os"

	matroska "github.com/dwbuiten/matroska"
)

func main() {
	if len(os.Args) < 2 {
		fmt.Println("Usage: go run debug_ebml.go <file>")
		os.Exit(1)
	}
	
	filename := os.Args[1]
	
	file, err := os.Open(filename)
	if err != nil {
		fmt.Printf("Error opening file: %v\n", err)
		os.Exit(1)
	}
	defer file.Close()
	
	fmt.Printf("Debugging EBML parsing for: %s\n", filename)
	
	// Create EBML reader
	reader := matroska.NewEBMLReader(file)
	
	fmt.Println("\nTrying to read first element...")
	
	// Try to read the first element
	element, err := reader.ReadElement()
	if err != nil {
		fmt.Printf("Error reading first element: %v\n", err)
		
		// Let's try to debug the VINT reading
		fmt.Println("\nDebugging VINT reading...")
		file.Seek(0, 0)
		reader = matroska.NewEBMLReader(file)
		
		// Try to read element ID
		fmt.Println("Reading element ID...")
		id, err := reader.ReadElementID()
		if err != nil {
			fmt.Printf("Error reading element ID: %v\n", err)
		} else {
			fmt.Printf("Element ID: 0x%08X\n", id)
		}
		
		// Try to read element size
		fmt.Println("Reading element size...")
		size, err := reader.ReadElementSize()
		if err != nil {
			fmt.Printf("Error reading element size: %v\n", err)
		} else {
			fmt.Printf("Element size: %d\n", size)
		}
		
		os.Exit(1)
	}
	
	fmt.Printf("Successfully read first element!\n")
	fmt.Printf("Element ID: 0x%08X\n", element.ID)
	fmt.Printf("Element size: %d\n", element.Size)
	fmt.Printf("Element offset: %d\n", element.Offset)
	fmt.Printf("Element data length: %d\n", len(element.Data))
	
	// Check if this is the EBML header
	if element.ID == 0x1A45DFA3 {
		fmt.Println("✓ This is the EBML header element")
		
		// Try to parse children
		fmt.Println("\nParsing EBML header children...")
		childReader := matroska.NewEBMLReader(&bytesReader{data: element.Data})
		
		for childReader.Position() < uint64(len(element.Data)) {
			child, err := childReader.ReadElement()
			if err != nil {
				fmt.Printf("Error reading child element: %v\n", err)
				break
			}
			
			fmt.Printf("  Child ID: 0x%08X, Size: %d\n", child.ID, child.Size)
			
			switch child.ID {
			case 0x4282: // DocType
				docType := child.ReadString()
				fmt.Printf("    DocType: %s\n", docType)
			case 0x4287: // DocTypeVersion
				version, _ := child.ReadUint()
				fmt.Printf("    DocTypeVersion: %d\n", version)
			case 0x4286: // EBMLVersion
				version, _ := child.ReadUint()
				fmt.Printf("    EBMLVersion: %d\n", version)
			}
		}
		
		// Try to read the next element (should be Segment)
		fmt.Println("\nTrying to read second element (Segment)...")
		secondElement, err := reader.ReadElement()
		if err != nil {
			fmt.Printf("Error reading second element: %v\n", err)
		} else {
			fmt.Printf("Second element ID: 0x%08X\n", secondElement.ID)
			fmt.Printf("Second element size: %d\n", secondElement.Size)
			
			if secondElement.ID == 0x18538067 {
				fmt.Println("✓ This is the Segment element")
			} else {
				fmt.Println("✗ This is not the expected Segment element")
			}
		}
		
	} else {
		fmt.Printf("✗ This is not the EBML header (expected 0x1A45DFA3)\n")
	}
}

// bytesReader implements io.ReadSeeker for byte slices
type bytesReader struct {
	data []byte
	pos  int
}

func (r *bytesReader) Read(p []byte) (n int, err error) {
	if r.pos >= len(r.data) {
		return 0, fmt.Errorf("EOF")
	}

	n = copy(p, r.data[r.pos:])
	r.pos += n
	return n, nil
}

func (r *bytesReader) Seek(offset int64, whence int) (int64, error) {
	switch whence {
	case 0: // SeekStart
		r.pos = int(offset)
	case 1: // SeekCurrent
		r.pos += int(offset)
	case 2: // SeekEnd
		r.pos = len(r.data) + int(offset)
	}
	
	if r.pos < 0 {
		r.pos = 0
	}
	if r.pos > len(r.data) {
		r.pos = len(r.data)
	}
	
	return int64(r.pos), nil
}
