package main

import (
	"fmt"
	"os"

	matroska "github.com/dwbuiten/matroska"
)

func main() {
	if len(os.Args) < 2 {
		fmt.Println("Usage: go run debug_parser.go <file>")
		os.Exit(1)
	}
	
	filename := os.Args[1]
	
	file, err := os.Open(filename)
	if err != nil {
		fmt.Printf("Error opening file: %v\n", err)
		os.Exit(1)
	}
	defer file.Close()
	
	fmt.Printf("Debugging parser for: %s\n", filename)
	
	// Create EBML reader
	reader := matroska.NewEBMLReader(file)
	
	// Read EBML header
	fmt.Println("Reading EBML header...")
	ebmlHeader, err := reader.ReadElement()
	if err != nil {
		fmt.Printf("Error reading EBML header: %v\n", err)
		os.Exit(1)
	}
	fmt.Printf("EBML Header: ID=0x%08X, Size=%d\n", ebmlHeader.ID, ebmlHeader.Size)
	
	// Read Segment
	fmt.Println("Reading Segment...")
	segmentElement, err := reader.ReadElement()
	if err != nil {
		fmt.Printf("Error reading Segment: %v\n", err)
		os.Exit(1)
	}
	fmt.Printf("Segment: ID=0x%08X, Size=%d, Offset=%d\n", 
		segmentElement.ID, segmentElement.Size, segmentElement.Offset)
	
	// Calculate segment position - we know from analysis it should be 52
	segmentPos := uint64(52)
	fmt.Printf("Segment data starts at: %d\n", segmentPos)
	
	// Seek to segment position
	if err := reader.Seek(segmentPos); err != nil {
		fmt.Printf("Error seeking to segment position: %v\n", err)
		os.Exit(1)
	}
	
	// Read first few elements in segment
	fmt.Println("\nReading segment elements...")
	for i := 0; i < 10; i++ {
		element, err := reader.ReadElement()
		if err != nil {
			fmt.Printf("Error reading element %d: %v\n", i, err)
			break
		}
		
		elementName := getElementName(element.ID)
		fmt.Printf("Element %d: ID=0x%08X (%s), Size=%d, HasData=%t\n", 
			i, element.ID, elementName, element.Size, element.Data != nil)
		
		// If this is a small element, show some details
		if element.Data != nil && len(element.Data) < 100 {
			switch element.ID {
			case 0x1549A966: // SegmentInfo
				fmt.Printf("  Found SegmentInfo!\n")
			case 0x1654AE6B: // Tracks
				fmt.Printf("  Found Tracks!\n")
			case 0x114D9B74: // SeekHead
				fmt.Printf("  Found SeekHead!\n")
			}
		}
		
		// Stop if we hit a cluster
		if element.ID == 0x1F43B675 { // Cluster
			fmt.Printf("  Reached first cluster, stopping\n")
			break
		}
	}
}

func getElementHeaderSize(element *matroska.EBMLElement) uint64 {
	idSize := getVINTSize(uint64(element.ID))
	sizeSize := getVINTSize(element.Size)
	return uint64(idSize + sizeSize)
}

func getVINTSize(value uint64) int {
	if value == 0 {
		return 1
	}
	
	size := 1
	for value >= (1 << (7 * size)) {
		size++
	}
	return size
}

func getElementName(id uint32) string {
	switch id {
	case 0x1A45DFA3:
		return "EBML"
	case 0x18538067:
		return "Segment"
	case 0x114D9B74:
		return "SeekHead"
	case 0x1549A966:
		return "SegmentInfo"
	case 0x1654AE6B:
		return "Tracks"
	case 0x1C53BB6B:
		return "Cues"
	case 0x1941A469:
		return "Attachments"
	case 0x1043A770:
		return "Chapters"
	case 0x1254C367:
		return "Tags"
	case 0x1F43B675:
		return "Cluster"
	default:
		return "Unknown"
	}
}
