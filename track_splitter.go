package main

import (
	"fmt"
	"io"
	"os"
	"path/filepath"
	"strings"

	"github.com/dwbuiten/matroska"
)

// TrackSplitter handles splitting MKV tracks into separate files
type TrackSplitter struct {
	inputFile  string
	outputDir  string
	demuxer    *matroska.Demuxer
	trackFiles map[uint8]*os.File
}

// NewTrackSplitter creates a new track splitter instance
func NewTrackSplitter(inputFile, outputDir string) *TrackSplitter {
	return &TrackSplitter{
		inputFile:  inputFile,
		outputDir:  outputDir,
		trackFiles: make(map[uint8]*os.File),
	}
}

// getTrackExtension returns appropriate file extension based on codec
func getTrackExtension(trackInfo *matroska.TrackInfo) string {
	codec := strings.ToLower(trackInfo.CodecID)
	
	switch trackInfo.Type {
	case matroska.TypeVideo:
		switch {
		case strings.Contains(codec, "h264"), strings.Contains(codec, "avc"):
			return ".h264"
		case strings.Contains(codec, "h265"), strings.Contains(codec, "hevc"):
			return ".h265"
		case strings.Contains(codec, "vp8"):
			return ".vp8"
		case strings.Contains(codec, "vp9"):
			return ".vp9"
		case strings.Contains(codec, "av1"):
			return ".av1"
		default:
			return ".video"
		}
	case matroska.TypeAudio:
		switch {
		case strings.Contains(codec, "aac"):
			return ".aac"
		case strings.Contains(codec, "mp3"):
			return ".mp3"
		case strings.Contains(codec, "ac3"):
			return ".ac3"
		case strings.Contains(codec, "dts"):
			return ".dts"
		case strings.Contains(codec, "flac"):
			return ".flac"
		case strings.Contains(codec, "opus"):
			return ".opus"
		case strings.Contains(codec, "vorbis"):
			return ".ogg"
		default:
			return ".audio"
		}
	case matroska.TypeSubtitle:
		switch {
		case strings.Contains(codec, "ass"), strings.Contains(codec, "ssa"):
			return ".ass"
		case strings.Contains(codec, "srt"):
			return ".srt"
		case strings.Contains(codec, "vtt"):
			return ".vtt"
		case strings.Contains(codec, "pgs"):
			return ".sup"
		default:
			return ".subtitle"
		}
	default:
		return ".track"
	}
}

// getTrackTypeString returns human-readable track type
func getTrackTypeString(trackType uint8) string {
	switch trackType {
	case matroska.TypeVideo:
		return "Video"
	case matroska.TypeAudio:
		return "Audio"
	case matroska.TypeSubtitle:
		return "Subtitle"
	default:
		return "Unknown"
	}
}

// Initialize opens the MKV file and sets up the demuxer
func (ts *TrackSplitter) Initialize() error {
	fmt.Printf("Opening MKV file: %s\n", ts.inputFile)
	
	file, err := os.Open(ts.inputFile)
	if err != nil {
		return fmt.Errorf("failed to open input file: %v", err)
	}
	
	// Create demuxer
	ts.demuxer, err = matroska.NewDemuxer(file)
	if err != nil {
		file.Close()
		return fmt.Errorf("failed to create demuxer: %v", err)
	}
	
	// Create output directory if it doesn't exist
	if err := os.MkdirAll(ts.outputDir, 0755); err != nil {
		return fmt.Errorf("failed to create output directory: %v", err)
	}
	
	return nil
}

// PrintFileInfo displays information about the MKV file
func (ts *TrackSplitter) PrintFileInfo() error {
	// Get file info
	fileInfo, err := ts.demuxer.GetFileInfo()
	if err != nil {
		return fmt.Errorf("failed to get file info: %v", err)
	}
	
	fmt.Println("\n=== File Information ===")
	if fileInfo.Title != "" {
		fmt.Printf("Title: %s\n", fileInfo.Title)
	}
	if fileInfo.MuxingApp != "" {
		fmt.Printf("Muxing App: %s\n", fileInfo.MuxingApp)
	}
	if fileInfo.WritingApp != "" {
		fmt.Printf("Writing App: %s\n", fileInfo.WritingApp)
	}
	fmt.Printf("Timecode Scale: %d\n", fileInfo.TimecodeScale)
	if fileInfo.Duration > 0 {
		fmt.Printf("Duration: %d timecode units\n", fileInfo.Duration)
	}
	
	// Get track count
	numTracks, err := ts.demuxer.GetNumTracks()
	if err != nil {
		return fmt.Errorf("failed to get track count: %v", err)
	}
	
	fmt.Printf("Number of tracks: %d\n", numTracks)
	
	// Print track information
	fmt.Println("\n=== Track Information ===")
	for i := uint(0); i < numTracks; i++ {
		trackInfo, err := ts.demuxer.GetTrackInfo(i)
		if err != nil {
			fmt.Printf("Failed to get track %d info: %v\n", i, err)
			continue
		}
		
		fmt.Printf("Track %d:\n", i)
		fmt.Printf("  Number: %d\n", trackInfo.Number)
		fmt.Printf("  Type: %s (%d)\n", getTrackTypeString(trackInfo.Type), trackInfo.Type)
		fmt.Printf("  Codec: %s\n", trackInfo.CodecID)
		if trackInfo.Name != "" {
			fmt.Printf("  Name: %s\n", trackInfo.Name)
		}
		if trackInfo.Language != "" {
			fmt.Printf("  Language: %s\n", trackInfo.Language)
		}
		
		// Video specific info
		if trackInfo.Type == matroska.TypeVideo && trackInfo.Video != nil {
			fmt.Printf("  Resolution: %dx%d\n", trackInfo.Video.PixelWidth, trackInfo.Video.PixelHeight)
			if trackInfo.Video.DisplayWidth > 0 && trackInfo.Video.DisplayHeight > 0 {
				fmt.Printf("  Display: %dx%d\n", trackInfo.Video.DisplayWidth, trackInfo.Video.DisplayHeight)
			}
		}
		
		// Audio specific info
		if trackInfo.Type == matroska.TypeAudio && trackInfo.Audio != nil {
			fmt.Printf("  Sample Rate: %.0f Hz\n", trackInfo.Audio.SamplingFreq)
			fmt.Printf("  Channels: %d\n", trackInfo.Audio.Channels)
			if trackInfo.Audio.BitDepth > 0 {
				fmt.Printf("  Bit Depth: %d\n", trackInfo.Audio.BitDepth)
			}
		}
		
		fmt.Printf("  Codec Private Size: %d bytes\n", len(trackInfo.CodecPrivate))
		fmt.Println()
	}
	
	return nil
}

// CreateTrackFiles creates output files for each track
func (ts *TrackSplitter) CreateTrackFiles() error {
	numTracks, err := ts.demuxer.GetNumTracks()
	if err != nil {
		return fmt.Errorf("failed to get track count: %v", err)
	}
	
	baseName := strings.TrimSuffix(filepath.Base(ts.inputFile), filepath.Ext(ts.inputFile))
	
	fmt.Println("\n=== Creating Track Files ===")
	for i := uint(0); i < numTracks; i++ {
		trackInfo, err := ts.demuxer.GetTrackInfo(i)
		if err != nil {
			return fmt.Errorf("failed to get track %d info: %v", i, err)
		}
		
		ext := getTrackExtension(trackInfo)
		fileName := fmt.Sprintf("%s_track_%d_%s%s", 
			baseName, 
			trackInfo.Number, 
			getTrackTypeString(trackInfo.Type), 
			ext)
		
		filePath := filepath.Join(ts.outputDir, fileName)
		
		file, err := os.Create(filePath)
		if err != nil {
			return fmt.Errorf("failed to create track file %s: %v", filePath, err)
		}
		
		ts.trackFiles[uint8(i)] = file
		fmt.Printf("Created: %s\n", filePath)
	}
	
	return nil
}

// SplitTracks reads packets and writes them to appropriate track files
func (ts *TrackSplitter) SplitTracks() error {
	fmt.Println("\n=== Splitting Tracks ===")
	
	packetCount := 0
	trackPacketCounts := make(map[uint8]int)
	
	for {
		packet, err := ts.demuxer.ReadPacket()
		if err != nil {
			if err == io.EOF {
				fmt.Println("Reached end of file")
				break
			}
			return fmt.Errorf("failed to read packet: %v", err)
		}
		
		if packet == nil {
			continue
		}
		
		// Find the corresponding file for this track
		file, exists := ts.trackFiles[packet.Track]
		if !exists {
			fmt.Printf("Warning: No output file for track %d\n", packet.Track)
			continue
		}
		
		// Write packet data to track file
		_, err = file.Write(packet.Data)
		if err != nil {
			return fmt.Errorf("failed to write packet data for track %d: %v", packet.Track, err)
		}
		
		packetCount++
		trackPacketCounts[packet.Track]++
		
		// Print progress every 1000 packets
		if packetCount%1000 == 0 {
			fmt.Printf("Processed %d packets...\n", packetCount)
		}
	}
	
	fmt.Printf("\nTotal packets processed: %d\n", packetCount)
	fmt.Println("Packets per track:")
	for track, count := range trackPacketCounts {
		fmt.Printf("  Track %d: %d packets\n", track, count)
	}
	
	return nil
}

// Close cleans up resources
func (ts *TrackSplitter) Close() {
	if ts.demuxer != nil {
		ts.demuxer.Close()
	}
	
	for _, file := range ts.trackFiles {
		if file != nil {
			file.Close()
		}
	}
}

func main() {
	if len(os.Args) < 2 {
		fmt.Println("Usage: go run track_splitter.go <input.mkv> [output_directory]")
		fmt.Println("Example: go run track_splitter.go /path/to/video.mkv ./output")
		os.Exit(1)
	}
	
	inputFile := os.Args[1]
	outputDir := "./output"
	
	if len(os.Args) >= 3 {
		outputDir = os.Args[2]
	}
	
	// Check if input file exists
	if _, err := os.Stat(inputFile); os.IsNotExist(err) {
		fmt.Printf("Error: Input file does not exist: %s\n", inputFile)
		os.Exit(1)
	}
	
	fmt.Printf("MKV Track Splitter\n")
	fmt.Printf("Input file: %s\n", inputFile)
	fmt.Printf("Output directory: %s\n", outputDir)
	
	splitter := NewTrackSplitter(inputFile, outputDir)
	defer splitter.Close()
	
	// Initialize
	if err := splitter.Initialize(); err != nil {
		fmt.Printf("Initialization failed: %v\n", err)
		os.Exit(1)
	}
	
	// Print file and track information
	if err := splitter.PrintFileInfo(); err != nil {
		fmt.Printf("Failed to print file info: %v\n", err)
		os.Exit(1)
	}
	
	// Create track files
	if err := splitter.CreateTrackFiles(); err != nil {
		fmt.Printf("Failed to create track files: %v\n", err)
		os.Exit(1)
	}
	
	// Split tracks
	if err := splitter.SplitTracks(); err != nil {
		fmt.Printf("Failed to split tracks: %v\n", err)
		os.Exit(1)
	}
	
	fmt.Println("\nTrack splitting completed successfully!")
}
