# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Overview

This is a Go library for parsing and demuxing Matroska/WebM multimedia container files. The library provides a complete implementation of the EBML (Extensible Binary Meta Language) parser and Matroska container format handling.

## Build and Development Commands

### Basic Commands
- `go build .` - Build the library (compile to test-output for testing per user requirements)
- `go test ./...` - Run all tests (if test files exist)
- `go mod tidy` - Clean up module dependencies
- `go vet .` - Run static analysis

### Test Compilation
For testing compilation without running:
```bash
go build -o test-output .
rm test-output  # Clean up after successful compilation
```

## Architecture Overview

### Core Components

1. **EBML Layer** (`ebml.go`)
   - Low-level EBML parser for reading variable-length integers and elements
   - `EBMLReader` handles the binary format parsing
   - `EBMLElement` represents parsed EBML elements

2. **Parser Layer** (`parser.go`)
   - High-level Matroska parser that interprets EBML elements
   - `<PERSON>rser` coordinates reading of segments, tracks, cues, chapters, etc.
   - Handles both streaming and seeking parsers

3. **Demuxer Interface** (`matroska.go`)
   - Main public API for users of the library
   - `Demuxer` provides track info, file metadata, and packet reading
   - Supports both regular and streaming modes

4. **Packet Reading** (`packet.go`)
   - `PacketReader` handles cluster parsing and packet extraction
   - Supports seeking with cues or linear scanning
   - Manages track masking for selective demuxing

### Data Flow

```
Input Stream → EBMLReader → Parser → Demuxer → PacketReader → Packets
```

### Key Data Structures

- **TrackInfo** (`types.go`): Complete track metadata including video/audio properties
- **Packet** (`types.go`): Demuxed media packet with timing and data
- **SegmentInfo** (`types.go`): File-level metadata
- **Attachment**, **Chapter**, **Tag**, **Cue**: Additional container metadata

### Element Parsing

The library uses a modular parsing approach:
- `segment.go` - Parses segment info, tracks, video/audio properties
- `parsing.go` - Parses attachments, chapters, tags, and cues
- Element IDs are defined in `elements.go` with comprehensive constants

### Streaming vs Seeking

- **Regular Demuxer**: Requires `io.ReadSeeker`, can seek efficiently
- **Streaming Demuxer**: Works with `io.Reader`, avoids seeks for streaming scenarios
- Internal `fakeSeeker` wrapper allows streaming mode with non-seeking readers

## Development Notes

### Error Handling
- Comprehensive error handling throughout the parsing chain
- Graceful handling of malformed or incomplete files
- EOF detection for streaming scenarios

### Memory Management
- Deep copying of metadata structures to prevent data races
- Efficient packet reading without unnecessary allocations
- Cluster-based processing for large files

### Compatibility
- Supports both Matroska (.mkv) and WebM formats
- Handles various compression methods and encodings
- Comprehensive color space and HDR metadata support

## Module Dependencies

- `github.com/pborman/uuid` - For generating unique identifiers
- Standard library only for core functionality

## File Organization

- Core parsing: `ebml.go`, `parser.go`, `matroska.go`
- Data structures: `types.go`, `elements.go`  
- Specialized parsing: `segment.go`, `parsing.go`
- Packet handling: `packet.go`
- Utilities: `util.go`

The codebase follows Go conventions with clear separation of concerns and comprehensive type definitions for the Matroska container format.