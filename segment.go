package matroska

import (
	"io"
)

func (p *Parser) parseSegmentInfo(element *EBMLElement) error {
	reader := NewEBMLReader(&bytesReader{data: element.Data})
	
	info := &SegmentInfo{
		TimecodeScale: 1000000,
	}
	
	for reader.Position() < uint64(len(element.Data)) {
		child, err := reader.ReadElement()
		if err != nil {
			if err == io.EOF {
				break
			}
			return err
		}
		
		switch child.ID {
		case TimecodeScaleID:
			scale, err := child.ReadUint()
			if err != nil {
				return err
			}
			info.TimecodeScale = scale
		case DurationID:
			duration, err := child.ReadFloat()
			if err != nil {
				return err
			}
			info.Duration = uint64(duration)
		case DateUTCID:
			date, err := child.ReadInt()
			if err != nil {
				return err
			}
			info.DateUTC = date
			info.DateUTCValid = true
		case TitleID:
			info.Title = child.ReadString()
		case MuxingAppID:
			info.MuxingApp = child.ReadString()
		case WritingAppID:
			info.WritingApp = child.ReadString()
		case SegmentUIDID:
			uid := child.ReadBytes()
			if len(uid) <= 16 {
				copy(info.UID[:], uid)
			}
		case SegmentFilenameID:
			info.Filename = child.ReadString()
		case PrevUIDID:
			uid := child.ReadBytes()
			if len(uid) <= 16 {
				copy(info.PrevUID[:], uid)
			}
		case PrevFilenameID:
			info.PrevFilename = child.ReadString()
		case NextUIDID:
			uid := child.ReadBytes()
			if len(uid) <= 16 {
				copy(info.NextUID[:], uid)
			}
		case NextFilenameID:
			info.NextFilename = child.ReadString()
		}
	}
	
	p.segmentInfo = info
	return nil
}

func (p *Parser) parseTracks(element *EBMLElement) error {
	reader := NewEBMLReader(&bytesReader{data: element.Data})
	
	for reader.Position() < uint64(len(element.Data)) {
		child, err := reader.ReadElement()
		if err != nil {
			if err == io.EOF {
				break
			}
			return err
		}
		
		if child.ID == TrackEntryID {
			track, err := p.parseTrackEntry(child)
			if err != nil {
				return err
			}
			p.tracks = append(p.tracks, track)
		}
	}
	
	return nil
}

func (p *Parser) parseTrackEntry(element *EBMLElement) (*TrackInfo, error) {
	reader := NewEBMLReader(&bytesReader{data: element.Data})
	
	track := &TrackInfo{
		Enabled:        true,
		Lacing:         true,
		Language:       "eng",
		TimecodeScale:  1.0,
	}
	
	for reader.Position() < uint64(len(element.Data)) {
		child, err := reader.ReadElement()
		if err != nil {
			if err == io.EOF {
				break
			}
			return nil, err
		}
		
		switch child.ID {
		case TrackNumberID:
			num, err := child.ReadUint()
			if err != nil {
				return nil, err
			}
			track.Number = uint8(num)
		case TrackUIDID:
			uid, err := child.ReadUint()
			if err != nil {
				return nil, err
			}
			track.UID = uid
		case TrackTypeID:
			trackType, err := child.ReadUint()
			if err != nil {
				return nil, err
			}
			track.Type = uint8(trackType)
		case FlagEnabledID:
			enabled, err := child.ReadUint()
			if err != nil {
				return nil, err
			}
			track.Enabled = enabled != 0
		case FlagDefaultID:
			def, err := child.ReadUint()
			if err != nil {
				return nil, err
			}
			track.Default = def != 0
		case FlagForcedID:
			forced, err := child.ReadUint()
			if err != nil {
				return nil, err
			}
			track.Forced = forced != 0
		case FlagLacingID:
			lacing, err := child.ReadUint()
			if err != nil {
				return nil, err
			}
			track.Lacing = lacing != 0
		case MinCacheID:
			cache, err := child.ReadUint()
			if err != nil {
				return nil, err
			}
			track.MinCache = cache
		case MaxCacheID:
			cache, err := child.ReadUint()
			if err != nil {
				return nil, err
			}
			track.MaxCache = cache
		case DefaultDurationID:
			duration, err := child.ReadUint()
			if err != nil {
				return nil, err
			}
			track.DefaultDuration = duration
		case CodecDelayID:
			delay, err := child.ReadUint()
			if err != nil {
				return nil, err
			}
			track.CodecDelay = delay
		case SeekPreRollID:
			preroll, err := child.ReadUint()
			if err != nil {
				return nil, err
			}
			track.SeekPreRoll = preroll
		case TrackTimecodeScaleID:
			scale, err := child.ReadFloat()
			if err != nil {
				return nil, err
			}
			track.TimecodeScale = scale
		case MaxBlockAdditionIDID:
			maxID, err := child.ReadUint()
			if err != nil {
				return nil, err
			}
			track.MaxBlockAdditionID = uint32(maxID)
		case NameID:
			track.Name = child.ReadString()
		case LanguageID:
			lang := child.ReadString()
			if len(lang) >= 3 {
				track.Language = lang[:3]
			} else {
				track.Language = lang
			}
		case CodecIDID:
			track.CodecID = child.ReadString()
		case CodecPrivateID:
			track.CodecPrivate = child.ReadBytes()
		case VideoID:
			if err := p.parseVideoInfo(child, track); err != nil {
				return nil, err
			}
		case AudioID:
			if err := p.parseAudioInfo(child, track); err != nil {
				return nil, err
			}
		case ContentEncodingsID:
			if err := p.parseContentEncodings(child, track); err != nil {
				return nil, err
			}
		}
	}
	
	return track, nil
}

func (p *Parser) parseVideoInfo(element *EBMLElement, track *TrackInfo) error {
	reader := NewEBMLReader(&bytesReader{data: element.Data})
	
	for reader.Position() < uint64(len(element.Data)) {
		child, err := reader.ReadElement()
		if err != nil {
			if err == io.EOF {
				break
			}
			return err
		}
		
		switch child.ID {
		case FlagInterlacedID:
			interlaced, err := child.ReadUint()
			if err != nil {
				return err
			}
			track.Video.Interlaced = interlaced != 0
		case StereoModeID:
			mode, err := child.ReadUint()
			if err != nil {
				return err
			}
			track.Video.StereoMode = uint8(mode)
		case PixelWidthID:
			width, err := child.ReadUint()
			if err != nil {
				return err
			}
			track.Video.PixelWidth = uint32(width)
		case PixelHeightID:
			height, err := child.ReadUint()
			if err != nil {
				return err
			}
			track.Video.PixelHeight = uint32(height)
		case DisplayWidthID:
			width, err := child.ReadUint()
			if err != nil {
				return err
			}
			track.Video.DisplayWidth = uint32(width)
		case DisplayHeightID:
			height, err := child.ReadUint()
			if err != nil {
				return err
			}
			track.Video.DisplayHeight = uint32(height)
		case DisplayUnitID:
			unit, err := child.ReadUint()
			if err != nil {
				return err
			}
			track.Video.DisplayUnit = uint8(unit)
		case AspectRatioTypeID:
			ratioType, err := child.ReadUint()
			if err != nil {
				return err
			}
			track.Video.AspectRatioType = uint8(ratioType)
		case PixelCropLeftID:
			crop, err := child.ReadUint()
			if err != nil {
				return err
			}
			track.Video.CropL = uint32(crop)
		case PixelCropTopID:
			crop, err := child.ReadUint()
			if err != nil {
				return err
			}
			track.Video.CropT = uint32(crop)
		case PixelCropRightID:
			crop, err := child.ReadUint()
			if err != nil {
				return err
			}
			track.Video.CropR = uint32(crop)
		case PixelCropBottomID:
			crop, err := child.ReadUint()
			if err != nil {
				return err
			}
			track.Video.CropB = uint32(crop)
		case ColourSpaceID:
			colorSpace, err := child.ReadUint()
			if err != nil {
				return err
			}
			track.Video.ColourSpace = uint32(colorSpace)
		case GammaValueID:
			gamma, err := child.ReadFloat()
			if err != nil {
				return err
			}
			track.Video.GammaValue = gamma
		case ColorID:
			if err := p.parseColorInfo(child, track); err != nil {
				return err
			}
		}
	}
	
	if track.Video.DisplayWidth == 0 {
		track.Video.DisplayWidth = track.Video.PixelWidth
	}
	if track.Video.DisplayHeight == 0 {
		track.Video.DisplayHeight = track.Video.PixelHeight
	}
	
	return nil
}

func (p *Parser) parseColorInfo(element *EBMLElement, track *TrackInfo) error {
	reader := NewEBMLReader(&bytesReader{data: element.Data})
	
	for reader.Position() < uint64(len(element.Data)) {
		child, err := reader.ReadElement()
		if err != nil {
			if err == io.EOF {
				break
			}
			return err
		}
		
		switch child.ID {
		case MatrixCoefficientsID:
			coeff, err := child.ReadUint()
			if err != nil {
				return err
			}
			track.Video.Colour.MatrixCoefficients = uint32(coeff)
		case BitsPerChannelID:
			bits, err := child.ReadUint()
			if err != nil {
				return err
			}
			track.Video.Colour.BitsPerChannel = uint32(bits)
		case ChromaSubsamplingHorzID:
			chroma, err := child.ReadUint()
			if err != nil {
				return err
			}
			track.Video.Colour.ChromaSubsamplingHorz = uint32(chroma)
		case ChromaSubsamplingVertID:
			chroma, err := child.ReadUint()
			if err != nil {
				return err
			}
			track.Video.Colour.ChromaSubsamplingVert = uint32(chroma)
		case CbSubsamplingHorzID:
			cb, err := child.ReadUint()
			if err != nil {
				return err
			}
			track.Video.Colour.CbSubsamplingHorz = uint32(cb)
		case CbSubsamplingVertID:
			cb, err := child.ReadUint()
			if err != nil {
				return err
			}
			track.Video.Colour.CbSubsamplingVert = uint32(cb)
		case ChromaSitingHorzID:
			siting, err := child.ReadUint()
			if err != nil {
				return err
			}
			track.Video.Colour.ChromaSitingHorz = uint32(siting)
		case ChromaSitingVertID:
			siting, err := child.ReadUint()
			if err != nil {
				return err
			}
			track.Video.Colour.ChromaSitingVert = uint32(siting)
		case RangeID:
			colorRange, err := child.ReadUint()
			if err != nil {
				return err
			}
			track.Video.Colour.Range = uint32(colorRange)
		case TransferCharacteristicsID:
			transfer, err := child.ReadUint()
			if err != nil {
				return err
			}
			track.Video.Colour.TransferCharacteristics = uint32(transfer)
		case PrimariesID:
			primaries, err := child.ReadUint()
			if err != nil {
				return err
			}
			track.Video.Colour.Primaries = uint32(primaries)
		case MaxCLLID:
			maxCLL, err := child.ReadUint()
			if err != nil {
				return err
			}
			track.Video.Colour.MaxCLL = uint32(maxCLL)
		case MaxFALLID:
			maxFALL, err := child.ReadUint()
			if err != nil {
				return err
			}
			track.Video.Colour.MaxFALL = uint32(maxFALL)
		case MasteringMetadataID:
			if err := p.parseMasteringMetadata(child, track); err != nil {
				return err
			}
		}
	}
	
	return nil
}

func (p *Parser) parseMasteringMetadata(element *EBMLElement, track *TrackInfo) error {
	reader := NewEBMLReader(&bytesReader{data: element.Data})
	
	for reader.Position() < uint64(len(element.Data)) {
		child, err := reader.ReadElement()
		if err != nil {
			if err == io.EOF {
				break
			}
			return err
		}
		
		switch child.ID {
		case PrimaryRChromaticityXID:
			val, err := child.ReadFloat()
			if err != nil {
				return err
			}
			track.Video.Colour.MasteringMetadata.PrimaryRChromaticityX = float32(val)
		case PrimaryRChromaticityYID:
			val, err := child.ReadFloat()
			if err != nil {
				return err
			}
			track.Video.Colour.MasteringMetadata.PrimaryRChromaticityY = float32(val)
		case PrimaryGChromaticityXID:
			val, err := child.ReadFloat()
			if err != nil {
				return err
			}
			track.Video.Colour.MasteringMetadata.PrimaryGChromaticityX = float32(val)
		case PrimaryGChromaticityYID:
			val, err := child.ReadFloat()
			if err != nil {
				return err
			}
			track.Video.Colour.MasteringMetadata.PrimaryGChromaticityY = float32(val)
		case PrimaryBChromaticityXID:
			val, err := child.ReadFloat()
			if err != nil {
				return err
			}
			track.Video.Colour.MasteringMetadata.PrimaryBChromaticityX = float32(val)
		case PrimaryBChromaticityYID:
			val, err := child.ReadFloat()
			if err != nil {
				return err
			}
			track.Video.Colour.MasteringMetadata.PrimaryBChromaticityY = float32(val)
		case WhitePointChromaticityXID:
			val, err := child.ReadFloat()
			if err != nil {
				return err
			}
			track.Video.Colour.MasteringMetadata.WhitePointChromaticityX = float32(val)
		case WhitePointChromaticityYID:
			val, err := child.ReadFloat()
			if err != nil {
				return err
			}
			track.Video.Colour.MasteringMetadata.WhitePointChromaticityY = float32(val)
		case LuminanceMaxID:
			val, err := child.ReadFloat()
			if err != nil {
				return err
			}
			track.Video.Colour.MasteringMetadata.LuminanceMax = float32(val)
		case LuminanceMinID:
			val, err := child.ReadFloat()
			if err != nil {
				return err
			}
			track.Video.Colour.MasteringMetadata.LuminanceMin = float32(val)
		}
	}
	
	return nil
}

func (p *Parser) parseAudioInfo(element *EBMLElement, track *TrackInfo) error {
	reader := NewEBMLReader(&bytesReader{data: element.Data})
	
	track.Audio.Channels = 1
	
	for reader.Position() < uint64(len(element.Data)) {
		child, err := reader.ReadElement()
		if err != nil {
			if err == io.EOF {
				break
			}
			return err
		}
		
		switch child.ID {
		case SamplingFrequencyID:
			freq, err := child.ReadFloat()
			if err != nil {
				return err
			}
			track.Audio.SamplingFreq = freq
		case OutputSamplingFrequencyID:
			freq, err := child.ReadFloat()
			if err != nil {
				return err
			}
			track.Audio.OutputSamplingFreq = freq
		case ChannelsID:
			channels, err := child.ReadUint()
			if err != nil {
				return err
			}
			track.Audio.Channels = uint8(channels)
		case BitDepthID:
			depth, err := child.ReadUint()
			if err != nil {
				return err
			}
			track.Audio.BitDepth = uint8(depth)
		}
	}
	
	if track.Audio.OutputSamplingFreq == 0 {
		track.Audio.OutputSamplingFreq = track.Audio.SamplingFreq
	}
	
	return nil
}

func (p *Parser) parseContentEncodings(element *EBMLElement, track *TrackInfo) error {
	reader := NewEBMLReader(&bytesReader{data: element.Data})
	
	for reader.Position() < uint64(len(element.Data)) {
		child, err := reader.ReadElement()
		if err != nil {
			if err == io.EOF {
				break
			}
			return err
		}
		
		if child.ID == ContentEncodingID {
			if err := p.parseContentEncoding(child, track); err != nil {
				return err
			}
		}
	}
	
	return nil
}

func (p *Parser) parseContentEncoding(element *EBMLElement, track *TrackInfo) error {
	reader := NewEBMLReader(&bytesReader{data: element.Data})
	
	for reader.Position() < uint64(len(element.Data)) {
		child, err := reader.ReadElement()
		if err != nil {
			if err == io.EOF {
				break
			}
			return err
		}
		
		switch child.ID {
		case ContentCompressionID:
			if err := p.parseContentCompression(child, track); err != nil {
				return err
			}
		}
	}
	
	return nil
}

func (p *Parser) parseContentCompression(element *EBMLElement, track *TrackInfo) error {
	reader := NewEBMLReader(&bytesReader{data: element.Data})
	
	for reader.Position() < uint64(len(element.Data)) {
		child, err := reader.ReadElement()
		if err != nil {
			if err == io.EOF {
				break
			}
			return err
		}
		
		switch child.ID {
		case ContentCompAlgoID:
			algo, err := child.ReadUint()
			if err != nil {
				return err
			}
			track.CompMethod = uint32(algo)
			track.CompEnabled = true
		case ContentCompSettingsID:
			track.CompMethodPrivate = child.ReadBytes()
		}
	}
	
	return nil
}